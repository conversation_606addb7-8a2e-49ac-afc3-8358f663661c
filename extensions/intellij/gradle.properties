# IntelliJ Platform Artifacts Repositories -> https://plugins.jetbrains.com/docs/intellij/intellij-artifacts.html
pluginGroup=com.taobao.aimi
pluginName=AIMI
pluginRepositoryUrl=https://code.alibaba-inc.com/liziyang.lzy/AIMI
# SemVer format -> https://semver.org
pluginVersion=0.0.3
# Supported build number ranges and IntelliJ Platform versions -> https://plugins.jetbrains.com/docs/intellij/build-number-ranges.html
pluginSinceBuild=223
pluginUntilBuild=
# IntelliJ Platform Properties -> https://plugins.jetbrains.com/docs/intellij/tools-gradle-intellij-plugin.html#configuration-intellij-extension
platformType=IU
platformVersion=2025.1
#org.gradle.java.home=/opt/homebrew/opt/openjdk@17
#platformVersion = LATEST-EAP-SNAPSHOT
# Plugin Dependencies -> https://plugins.jetbrains.com/docs/intellij/plugin-dependencies.html
# Example: platformBundledPlugins = com.intellij.java, com.jetbrains.php:203.4449.22
platformPlugins=
#platformBundledPlugins=
platformBundledPlugins=com.intellij.java,org.jetbrains.kotlin,JavaScript
# Gradle Releases -> https://github.com/gradle/gradle/releases
gradleVersion=8.13
# Opt-out flag for bundling Kotlin standard library -> https://jb.gg/intellij-platform-kotlin-stdlib
kotlin.stdlib.default.dependency=false
# Enable Gradle Configuration Cache -> https://docs.gradle.org/current/userguide/configuration_cache.html
org.gradle.configuration-cache=true
# Enable Gradle Build Cache -> https://docs.gradle.org/current/userguide/build_cache.html
org.gradle.caching=true
# Enable Gradle Kotlin DSL Lazy Property Assignment -> https://docs.gradle.org/current/userguide/kotlin_dsl.html#kotdsl:assignment
systemProp.org.gradle.unsafe.kotlin.assignment=true
org.jetbrains.intellij.platform.downloadSources=true
kotlin.k2.enabled=true