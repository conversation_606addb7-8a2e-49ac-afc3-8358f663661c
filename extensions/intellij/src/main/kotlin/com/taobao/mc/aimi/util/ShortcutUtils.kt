package com.taobao.mc.aimi.util

import com.intellij.openapi.actionSystem.ActionManager
import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.KeyboardShortcut
import com.intellij.openapi.actionSystem.MouseShortcut
import com.intellij.openapi.keymap.KeymapManager
import com.intellij.openapi.keymap.KeymapUtil

object ShortcutUtils {

    /**
     * 获取指定 Action ID 的快捷键
     */
    fun getShortcutsForAction(actionId: String): List<String> {
        val shortcuts = mutableListOf<String>()

        try {
            val keymap = KeymapManager.getInstance().activeKeymap
            val keyStrokes = keymap.getShortcuts(actionId)

            keyStrokes.forEach { shortcut ->
                when (shortcut) {
                    is KeyboardShortcut -> {
                        val firstKeyStroke = shortcut.firstKeyStroke
                        val secondKeyStroke = shortcut.secondKeyStroke

                        var shortcutText = KeymapUtil.getKeystrokeText(firstKeyStroke)
                        if (secondKeyStroke != null) {
                            shortcutText += ", ${KeymapUtil.getKeystrokeText(secondKeyStroke)}"
                        }
                        shortcuts.add(shortcutText)
                    }

                    is MouseShortcut -> {
                        shortcuts.add(KeymapUtil.getMouseShortcutText(shortcut))
                    }
                }
            }
        } catch (e: Exception) {
            println("获取快捷键时发生错误: ${e.message}")
        }

        return shortcuts
    }

    /**
     * 获取 Action 实例的快捷键
     */
    fun getShortcutsForAction(action: AnAction): List<String> {
        val actionManager = ActionManager.getInstance()
        val actionId = actionManager.getId(action)

        return if (actionId != null) {
            getShortcutsForAction(actionId)
        } else {
            emptyList()
        }
    }

    /**
     * 通过 actionId 获取 Action 的快捷键、文本和描述信息
     */
    fun getActionInfo(actionId: String): Triple<String, String, String> {
        val shortcut = getShortcutsForAction(actionId).firstOrNull() ?: ""
        try {
            val actionManager = ActionManager.getInstance()
            val action = actionManager.getAction(actionId)

            val text = action?.templateText ?: ""
            val description = action?.templatePresentation?.description ?: ""

            return Triple(text, shortcut, description)
        } catch (_: Exception) {
            return Triple("", shortcut, "")
        }
    }
}