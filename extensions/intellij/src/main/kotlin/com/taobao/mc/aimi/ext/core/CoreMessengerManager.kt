package com.taobao.mc.aimi.ext.core

import com.intellij.openapi.Disposable
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.intellij.openapi.util.Disposer
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.services.TelemetryService
import com.taobao.mc.aimi.ext.utils.castNestedOrNull
import com.taobao.mc.aimi.ext.utils.getMachineUniqueID
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.protocol.IdeProtocolClient
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.cancel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

class CoreMessengerManager(
    private val project: Project,
    private val ideProtocolClient: IdeProtocolClient,
    private val coroutineScope: CoroutineScope
) : Disposable {
    var coreMessenger: CoreMessenger = createCoreMessenger()
    private var lastBackoffInterval = 0.5
    private val logger = LoggerManager.getLogger(CoreMessengerManager::class.java)
    private var isDisposed = false

    init {
        setupAnonymousTelemetry()
        // 注册 dispose 事件
        val service = project.service<AIMIPluginService>()
        Disposer.register(service, this)
    }

    fun restart() {
        coroutineScope.launch {
            try {
                coreMessenger.killSubProcess()
                lastBackoffInterval *= 2
                logger.warn("AIMI process exited, retrying in $lastBackoffInterval seconds")
                delay((lastBackoffInterval * 1000).toLong())
                if (isDisposed) return@launch
                coreMessenger = createCoreMessenger()
            } catch (e: Exception) {
                project.service<TelemetryService>().capture("jetbrains_core_start_error", mapOf("error" to e))
            }
        }
    }

    private fun createCoreMessenger() = CoreMessenger(project, ideProtocolClient, coroutineScope, ::restart)

    private fun setupAnonymousTelemetry() {
        coreMessenger.request(MessageTypes.ToCore.ConfigGetSerializedProfileInfo, null, null) { response ->
            val allowAnonymousTelemetry = response.castNestedOrNull<Boolean>("content", "result", "config", "allowAnonymousTelemetry")
            if (allowAnonymousTelemetry == true || allowAnonymousTelemetry == null)
                project.service<TelemetryService>().setup(getMachineUniqueID())
        }
    }

    /**
     * 清理资源，停止重试机制
     */
    override fun dispose() {
        logger.info("start dispose")
        isDisposed = true
        coroutineScope.cancel()
        coreMessenger.killSubProcess()
    }
}
