package com.taobao.mc.aimi.util

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.ScrollType
import com.intellij.openapi.editor.markup.EffectType
import com.intellij.openapi.editor.markup.HighlighterLayer
import com.intellij.openapi.editor.markup.HighlighterTargetArea
import com.intellij.openapi.editor.markup.TextAttributes
import com.intellij.openapi.fileEditor.FileDocumentManager
import com.intellij.openapi.fileEditor.FileEditor
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.TextEditor
import com.intellij.openapi.project.Project
import com.intellij.ui.JBColor
import com.taobao.mc.aimi.ext.core.UriUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch


val FileEditor.editor: Editor?
    get() = (this as? TextEditor)?.editor

fun FileEditor.readText(): String {
    return editor?.document?.text
        ?: run {
            file?.let {
                FileDocumentManager.getInstance().getDocument(it)?.text
            } ?: ""
        }
}

val FileEditorManager.mSelectedTextEditor: Editor?
    get() = runCatching { selectedTextEditor ?: selectedEditor?.editor }.getOrNull()

fun Project.showLines(filepath: String, startLine: Int, endLine: Int) {
    // 等待文件打开后跳转到指定行
    val file = UriUtils.uriToVirtualFile(filepath)
    file?.let { virtualFile ->
        ApplicationManager.getApplication().invokeLater {
            val fileEditorManager = FileEditorManager.getInstance(this)
            val textEditor = fileEditorManager.getSelectedEditor(virtualFile)?.editor

            textEditor?.let { editor ->
                // 确保行号在有效范围内
                val document = editor.document
                val totalLines = document.lineCount
                val safeStartLine = (startLine - 1).coerceIn(0, totalLines - 1) // 转换为0基索引
                val safeEndLine = (endLine - 1).coerceIn(safeStartLine, totalLines - 1)

                // 跳转到起始行
                val startOffset = document.getLineStartOffset(safeStartLine)
                val endOffset = document.getLineEndOffset(safeEndLine)

                // 移动光标到起始位置
                editor.caretModel.moveToOffset(startOffset)

                // 滚动到可见区域
                editor.scrollingModel.scrollToCaret(ScrollType.CENTER)

                // 高亮显示指定行范围
                val markupModel = editor.markupModel
                val textAttributes = TextAttributes().apply {
                    backgroundColor = JBColor.YELLOW.darker().darker()
                    effectType = EffectType.ROUNDED_BOX
                    effectColor = JBColor.ORANGE
                }

                // 添加高亮，2秒后自动移除
                val highlighter = markupModel.addRangeHighlighter(
                    startOffset,
                    endOffset,
                    HighlighterLayer.SELECTION,
                    textAttributes,
                    HighlighterTargetArea.EXACT_RANGE
                )

                // 2秒后移除高亮
                CoroutineScope(Dispatchers.Default).launch {
                    delay(2000)
                    ApplicationManager.getApplication().invokeLater {
                        if (highlighter.isValid) {
                            markupModel.removeHighlighter(highlighter)
                        }
                    }
                }
            }
        }
    }
}