package com.taobao.mc.aimi.settings

import com.intellij.openapi.application.ApplicationInfo
import com.taobao.mc.aimi.types.UserInfo
import kotlinx.serialization.Contextual
import kotlinx.serialization.KSerializer
import kotlinx.serialization.Serializable
import kotlinx.serialization.descriptors.PrimitiveKind
import kotlinx.serialization.descriptors.PrimitiveSerialDescriptor
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import java.util.concurrent.atomic.AtomicInteger

@Serializable
data class AIMIState(
    var enableTabAutocomplete: Boolean = true,
    var enableCrossFileComplete: Boolean = true,
    var enableOSR: Boolean = shouldRenderOffScreen(),
    var multilineCompletions: String = "auto", // auto / always / never
    var indexStatus: IndexUpdateStatus = IndexUpdateStatus(),
    var debounceDelay: Int = 500,
    // 未实现
    var showIDECompletionSideBySide: Boolean = true,
    var displayEditorTooltip: Boolean = true,
    var lastSelectedInlineEditModel: String? = null,
    var environment: String = "release",
    var userInfo: UserInfo? = null,
) {
    @Contextual
    val modify = AtomicInteger(0)
}

@Serializable
data class IndexUpdateStatus(
    var progress: Float = 0F,
    @Serializable(StatusSerializer::class)
    var status: IndexState = IndexState.DONE,
    var desc: String = "",
    var debugInfo: String? = null,
    var shouldClearIndexes: Boolean? = null,
)

@Serializable
enum class IndexState() {
    LOADING,
    INDEXING,
    WAITING,
    DONE,
    FAILED,
    PAUSED,
    DISABLED,
    CANCELLED;
}

/**
 * This function checks if off-screen rendering (OSR) should be used.
 */
private fun shouldRenderOffScreen(): Boolean {
    val minBuildNumber = 233
    val applicationInfo = ApplicationInfo.getInstance()
    val currentBuildNumber = applicationInfo.build.baselineVersion
    return currentBuildNumber >= minBuildNumber
}

private object StatusSerializer : KSerializer<IndexState> {
    override val descriptor = PrimitiveSerialDescriptor("Status", PrimitiveKind.STRING)

    override fun serialize(encoder: Encoder, value: IndexState) {
        encoder.encodeString(value.name.lowercase())
    }

    override fun deserialize(decoder: Decoder): IndexState {
        val string = decoder.decodeString()
        return IndexState.valueOf(string.uppercase())
    }
}