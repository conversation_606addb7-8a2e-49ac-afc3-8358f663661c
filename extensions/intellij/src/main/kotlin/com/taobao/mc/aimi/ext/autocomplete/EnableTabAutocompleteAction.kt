package com.taobao.mc.aimi.ext.autocomplete

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.components.service
import com.taobao.mc.aimi.settings.AIMISettingService

class EnableTabAutocompleteAction : AnAction("开启自动补全") {
    override fun actionPerformed(e: AnActionEvent) {
        val continueSettingsService = service<AIMISettingService>()
        continueSettingsService.state.enableTabAutocomplete = true
    }
}
