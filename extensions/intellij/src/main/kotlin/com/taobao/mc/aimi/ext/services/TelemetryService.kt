package com.taobao.mc.aimi.ext.services

import com.intellij.openapi.components.Service
import com.intellij.openapi.components.service
import com.intellij.openapi.project.Project
import com.taobao.mc.aimi.types.MessageTypes
import kotlinx.coroutines.launch

@Service(Service.Level.PROJECT)
class TelemetryService(private val project: Project) {
    // private val POSTHOG_API_KEY = "phc_JS6XFROuNbhJtVCEdTSYk6gl5ArRrTNMpCcguAXlSPs"
    // private var posthog: PostHog? = null;
    // private var distinctId: String? = null;

    fun setup(distinctId: String) {
        // this.posthog = Builder(POSTHOG_API_KEY).host("https://app.posthog.com").build()
        // this.distinctId = distinctId
    }

    fun capture(eventName: String, properties: Map<String, *>) {
        // if (this.posthog == null || this.distinctId == null) {
        //     return;
        // }
        // try {
        //     this.posthog?.capture(this.distinctId, eventName, properties)
        // } catch (e: Exception) {}
    }

    /**
     * 上传埋点事件到 ext
     * @param eventType 事件类型，如 autocomplete_invoke_start, autocomplete_accept 等
     * @param args 事件参数，包含 event, startTime, eventType, localRequestId, codeContext 等
     */
    fun eventTrack(eventType: String, args: Map<String, Any>) {
        try {
            with(project.service<AIMIPluginService>()) {
                coroutineScope.launch {
                    val coreMessenger = awaitCoreMessenger()
                    val data = mapOf(
                        "eventType" to eventType,
                        "args" to args
                    )
                    coreMessenger.request(MessageTypes.ToCore.EventTrack, data)
                }
            }
        } catch (_: Exception) {
            // 静默处理异常，避免影响主要功能
        }
    }

    fun shutdown() {
        // this.posthog?.shutdown()
    }
}