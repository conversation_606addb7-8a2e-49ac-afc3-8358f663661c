package com.taobao.mc.aimi.actions

import com.intellij.openapi.actionSystem.AnAction
import com.intellij.openapi.actionSystem.AnActionEvent
import com.intellij.openapi.application.EDT
import com.intellij.openapi.ui.Messages
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.search.SymbolSearchService
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext

/**
 * 搜索Action - 调用SearchEngine的search接口
 */
class SearchAction : AnAction("搜索") {
    private val logger = LoggerManager.getLogger(SearchAction::class.java)
    private val searchScope = CoroutineScope(Dispatchers.IO)

    override fun actionPerformed(e: AnActionEvent) {
        val project = e.project ?: return

        logger.info("执行搜索Action")

        // 弹出输入对话框获取搜索关键词
        val query = Messages.showInputDialog(
            project,
            "请输入搜索关键词:",
            "AIMI 搜索",
            Messages.getQuestionIcon()
        )

        if (query.isNullOrBlank()) {
            logger.info("用户取消搜索或输入为空")
            return
        }

        // 异步执行搜索
        searchScope.launch(Dispatchers.EDT) {
            try {
                logger.info("开始搜索: query='$query'")
                // searchEngine.combinedSearch(query.trim(), callback = progressCallback)
                SymbolSearchService().searchSymbols(project, query.trim(), query.trim().length, e)
            } catch (e: Exception) {
                logger.warn("搜索异常: ${e.message}", e)
                withContext(Dispatchers.Main) {
                    Messages.showErrorDialog(
                        project,
                        "搜索异常: ${e.message}",
                        "搜索错误"
                    )
                }
            }

        }
    }
}