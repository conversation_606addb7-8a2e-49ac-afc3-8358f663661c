package com.taobao.mc.aimi.startup

import com.intellij.ide.plugins.PluginManagerCore
import com.intellij.ide.ui.LafManagerListener
import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.application.EDT
import com.intellij.openapi.components.service
import com.intellij.openapi.extensions.PluginId
import com.intellij.openapi.fileEditor.FileEditorManager
import com.intellij.openapi.fileEditor.FileEditorManagerListener
import com.intellij.openapi.project.Project
import com.intellij.openapi.startup.ProjectActivity
import com.intellij.openapi.vfs.VirtualFile
import com.intellij.openapi.vfs.VirtualFileManager
import com.intellij.openapi.vfs.newvfs.BulkFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileContentChangeEvent
import com.intellij.openapi.vfs.newvfs.events.VFileCreateEvent
import com.intellij.openapi.vfs.newvfs.events.VFileDeleteEvent
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.intellij.util.messages.MessageBusConnection
import com.intellij.util.text.VersionComparatorUtil
import com.taobao.mc.aimi.ext.IDE
import com.taobao.mc.aimi.ext.ToastType
import com.taobao.mc.aimi.ext.actions.toggleAIMI
import com.taobao.mc.aimi.ext.activities.AIMIPluginDisposable
import com.taobao.mc.aimi.ext.constants.AIMIConstants
import com.taobao.mc.aimi.ext.core.CoreMessenger
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.ext.utils.toUriOrNull
import com.taobao.mc.aimi.listeners.ActiveEditorChangeListener
import com.taobao.mc.aimi.listeners.ThemeChangeListener
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.services.UIFreezeDetectionService
import com.taobao.mc.aimi.settings.AIMISettingsListener
import com.taobao.mc.aimi.settings.AIMIState
import com.taobao.mc.aimi.types.MessageTypes.ToCore
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.delay
import kotlinx.coroutines.withContext
import okhttp3.OkHttpClient
import okhttp3.Request
import okio.IOException
import java.awt.Desktop
import java.net.URI
import java.net.URL
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

/**
 * AIMI 启动活动
 */
class AIMIStartupActivity : ProjectActivity {

    private val logger = LoggerManager.getLogger(javaClass)

    override suspend fun execute(project: Project) {
        // 注册主题变更监听器
        val disposable = AIMIPluginDisposable.getInstance(project)
        val connection = ApplicationManager.getApplication().messageBus.connect(disposable)
        connection.subscribe(LafManagerListener.TOPIC, ThemeChangeListener())

        // 注册活动编辑器变化监听器
        val activeEditorChangeListener = ActiveEditorChangeListener(project)
        connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, activeEditorChangeListener)

        val aimiPluginService = project.service<AIMIPluginService>()
        val coreMessenger = aimiPluginService.awaitCoreMessenger()
        val ideProtocolClient = aimiPluginService.awaitIdeProtocolClient()
        // 工程打开后, 进行一次 indexing
        coreMessenger.request(
            messageType = ToCore.IndexForceReIndex,
            data = mapOf("shouldClearIndexes" to false)
        )
        // 工程打开后, 透传 当前已打开的文件
        ideProtocolClient.ide.getOpenFiles().let {
            // 延时2s, 等待 ext 进程初始化完成
            delay(2000)
            logger.debug("getOpenFiles size ${it.size}")
            coreMessenger.request(
                ToCore.FilesOpened,
                data = mapOf("uris" to it)
            )
        }
        // 主动打开AIMI窗口
        withContext(Dispatchers.EDT) {
            toggleAIMI(project, false)
        }

        // 启动UI冻结检测服务
        try {
            val uiFreezeDetectionService = project.service<UIFreezeDetectionService>()
            uiFreezeDetectionService.startDetection()
            logger.info("UI冻结检测服务已启动")
        } catch (e: Exception) {
            logger.warn("启动UI冻结检测服务失败", e)
        }

        // 监听 AIMI 设置, 将 部分设置同步到 ext
        connection.subscribe(AIMISettingsListener.TOPIC, object : AIMISettingsListener {
            private val lastModified = AtomicInteger(0)
            override fun settingsUpdated(settings: AIMIState) {
                if (settings.modify.get() == lastModified.get()) {
                    return
                }
                lastModified.set(settings.modify.get())

                // 更新所有窗口的URL（如果环境发生变化）
                aimiPluginService.updateAllWindowsUrl()

                coreMessenger.request(
                    messageType = ToCore.ConfigUpdateSharedConfig,
                    data = mapOf(
                        "useAutocompleteMultilineCompletions" to settings.multilineCompletions,
                        "enableCrossFileComplete" to settings.enableCrossFileComplete,
                        "debounceDelay" to settings.debounceDelay,
                        "environment" to settings.environment
                    )
                ) {
                    logger.info("updatedSharedConfig: $it")
                }
            }
        })

        subscribeFiles(connection, coreMessenger)


        withContext(Dispatchers.Default) {
            checkUpdate(ideProtocolClient.ide)
        }
    }

    private fun subscribeFiles(connection: MessageBusConnection, coreMessenger: CoreMessenger) {
        // 监听文件变化, 并发送到 ext
        connection.subscribe(VirtualFileManager.VFS_CHANGES, object : BulkFileListener {
            override fun after(events: List<VFileEvent>) {
                val deletedURIs = events.filterIsInstance<VFileDeleteEvent>()
                    .mapNotNull { event -> event.file.toUriOrNull() }
                if (deletedURIs.isNotEmpty()) {
                    val data = mapOf("uris" to deletedURIs)
                    coreMessenger.request(ToCore.FilesDeleted, data)
                }
                val changedURIs = events.filterIsInstance<VFileContentChangeEvent>()
                    .mapNotNull { event -> event.file.toUriOrNull() }
                if (changedURIs.isNotEmpty()) {
                    val data = mapOf("uris" to changedURIs)
                    coreMessenger.request(ToCore.FilesChanged, data)
                }
                events.filterIsInstance<VFileCreateEvent>()
                    .mapNotNull { event -> event.file?.toUriOrNull() }
                    .takeIf { it.isNotEmpty() }?.let {
                        val data = mapOf("uris" to it)
                        coreMessenger.request(ToCore.FilesCreated, data)
                    }
            }
        })
        // 监听文件编辑器变化, 并发送到 ext
        connection.subscribe(FileEditorManagerListener.FILE_EDITOR_MANAGER, object : FileEditorManagerListener {
            override fun fileClosed(source: FileEditorManager, file: VirtualFile) {
                file.toUriOrNull()?.let { uri ->
                    val data = mapOf("uris" to listOf(uri))
                    coreMessenger.request(ToCore.FilesClosed, data)
                }
            }

            override fun fileOpened(source: FileEditorManager, file: VirtualFile) {
                file.toUriOrNull()?.let { uri ->
                    val data = mapOf("uris" to listOf(uri))
                    coreMessenger.request(ToCore.FilesOpened, data)
                }
            }
        })
    }

    private suspend fun checkUpdate(ide: IDE) {
        val (remoteVersion, remoteBuildTime) = runCatching {
            fetchRemoteVersionInfo()
        }.onFailure {
            logger.warn("checkUpdate failed", it)
        }.getOrNull() ?: return
        val pluginId = AIMIConstants.PLUGIN_ID
        val plugin = PluginManagerCore.getPlugin(PluginId.getId(pluginId))
        val extensionVersion = plugin?.version ?: return

        val dateFormat = SimpleDateFormat("yyyy-MM-dd HH:mm", Locale.getDefault())
        val pluginVersion = extensionVersion
        val pluginBuildTime = plugin.vendorUrl ?: ""

        val now = Date()
        val pluginBuildDate = runCatching { dateFormat.parse(pluginBuildTime) }.getOrNull() ?: now
        val remoteBuildDate = runCatching { dateFormat.parse(remoteBuildTime) }.getOrNull() ?: now

        val compare = VersionComparatorUtil.compare(remoteVersion, pluginVersion)
        var result: Any = ""
        val updateButtonTxt = "去更新"
        if (compare == 0) {
            // 版本相同，检查构建时间
            if (pluginBuildDate.before(remoteBuildDate)) {
                // 插件构建时间较旧，提示更新
                val message = "AIMI 插件有新版本，请更新"
                result = ide.showToast(ToastType.INFO, message, updateButtonTxt)
            }
        } else if (compare > 0) {
            // 插件版本较旧，提示更新
            val message = "AIMI 插件有新版本，请更新"
            result = ide.showToast(ToastType.INFO, message, updateButtonTxt)
        }
        if (result === updateButtonTxt) {
            Desktop.getDesktop().browse(URI(getDownloadURLForCurrentArchitecture()))
        }
    }

    private suspend fun fetchRemoteVersionInfo(): Pair<String, String>? {
        val url = runCatching { URL(getDownloadURLForCurrentArchitecture()) }.getOrElse {
            throw UpdateError.InvalidURL
        }

        val client = OkHttpClient.Builder()
            .connectTimeout(10, TimeUnit.SECONDS)
            .readTimeout(10, TimeUnit.SECONDS)
            .build()

        val request = Request.Builder()
            .url(url)
            .head()
            .build()

        return withContext(Dispatchers.IO) {
            try {
                val response = client.newCall(request).execute()

                if (!response.isSuccessful) {
                    throw UpdateError.HttpError(response.code)
                }

                val version = response.header("x-oss-meta-version")
                    ?: throw UpdateError.MissingHeaders("x-oss-meta-version")
                val buildTime = response.header("x-oss-meta-build-time")
                    ?: throw UpdateError.MissingHeaders("x-oss-meta-build-time")

                logger.info("[GeneralSettings] 获取到远程版本信息: version=$version, buildTime=$buildTime")

                Pair(version, buildTime)
            } catch (e: IOException) {
                throw UpdateError.NetworkError(e)
            } finally {
                client.dispatcher.executorService.shutdown()
            }
        }
    }

    // 对应的错误类型定义
    sealed class UpdateError : Exception() {
        data object InvalidURL : UpdateError()
        data class HttpError(val code: Int) : UpdateError()
        data class MissingHeaders(val header: String) : UpdateError()
        data class NetworkError(override val cause: Throwable) : UpdateError()
    }


    private fun getDownloadURLForCurrentArchitecture(): String {
        return "https://aimi-ide.oss-cn-zhangjiakou.aliyuncs.com/aimi-plugin/idea/latest/AIMI.zip"
    }

}