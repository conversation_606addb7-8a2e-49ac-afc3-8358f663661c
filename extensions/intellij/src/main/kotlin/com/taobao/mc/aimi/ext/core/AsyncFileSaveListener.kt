package com.taobao.mc.aimi.ext.core

import com.intellij.openapi.vfs.AsyncFileListener
import com.intellij.openapi.vfs.newvfs.events.VFileEvent
import com.taobao.mc.aimi.ext.services.AIMIPluginService
import com.taobao.mc.aimi.types.MessageTypes

class AsyncFileSaveListener(private val continuePluginService: AIMIPluginService) : AsyncFileListener {
    private val configFilePatterns = listOf(
        ".aimi/config.json",
        ".aimi/config.ts",
        ".aimi/config.yaml",
        ".aimi_rc.json"
    )

    override fun prepareChange(events: MutableList<out VFileEvent>): AsyncFileListener.ChangeApplier? {
        val isConfigFile = events.any { event ->
            configFilePatterns.any { pattern ->
                event.path.endsWith(pattern) || event.path.endsWith(pattern.replace("/", "\\"))
            }
        }

        return if (isConfigFile) {
            object : AsyncFileListener.ChangeApplier {
                override fun afterVfsChange() {
                    continuePluginService.coreMessenger?.request(MessageTypes.ToCore.ConfigReload, null, null) { _ -> }
                }
            }
        } else null
    }
}