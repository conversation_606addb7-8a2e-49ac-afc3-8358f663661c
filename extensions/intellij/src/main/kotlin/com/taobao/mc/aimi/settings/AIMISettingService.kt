package com.taobao.mc.aimi.settings

import com.intellij.openapi.application.ApplicationManager
import com.intellij.openapi.components.PersistentStateComponent
import com.intellij.openapi.components.State
import com.intellij.openapi.components.Storage
import com.intellij.openapi.components.service
import com.taobao.mc.aimi.logger.LoggerManager
import com.taobao.mc.aimi.types.UserInfo


@State(name = "com.taobao.mc.aimi.settings.AIMISettingService", storages = [Storage("AIMISettingService.xml")])
open class AIMISettingService : PersistentStateComponent<AIMIState> {
    private val logger = LoggerManager.getLogger(AIMISettingService::class.java)

    private var aimiState: AIMIState = AIMIState()

    override fun getState(): AIMIState {
        return aimiState
    }

    override fun loadState(state: AIMIState) {
        aimiState = state
    }

    fun notifySettingsUpdated() {
        ApplicationManager.getApplication().messageBus.syncPublisher(AIMISettingsListener.TOPIC).settingsUpdated(aimiState)
    }

    fun updateUserInfo(userInfo: UserInfo) {
        aimiState.userInfo = userInfo
        notifySettingsUpdated()
    }

    companion object {
        val instance: AIMISettingService
            get() = service<AIMISettingService>()
    }

}