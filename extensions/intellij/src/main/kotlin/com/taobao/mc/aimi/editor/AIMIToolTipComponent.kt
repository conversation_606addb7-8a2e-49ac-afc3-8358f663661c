package com.taobao.mc.aimi.editor

import com.intellij.openapi.editor.Editor
import com.intellij.openapi.editor.colors.EditorColorsManager
import com.intellij.openapi.editor.colors.EditorFontType
import com.intellij.ui.components.JBPanel
import com.taobao.mc.aimi.ext.actions.focusContinueInput
import com.taobao.mc.aimi.util.AIMIIcons
import com.taobao.mc.aimi.util.ShortcutUtils
import java.awt.*
import java.awt.event.ActionEvent
import java.awt.event.MouseAdapter
import java.awt.event.MouseEvent
import java.awt.geom.RoundRectangle2D
import javax.swing.JButton
import javax.swing.JLabel

class AIMIStyledButton(text: String) : JButton(text) {
    companion object {
        val hoverColor: Color = Color(98, 100, 167).brighter() // 紫色高亮
        val normalColor: Color = Color(60, 63, 65) // 与弹框背景相同的深灰色
    }

    private var isHovered = false

    init {
        border = null
        isContentAreaFilled = false
        isFocusPainted = false
        cursor = Cursor(Cursor.HAND_CURSOR)

        val scheme = EditorColorsManager.getInstance().globalScheme
        val editorFont = scheme.getFont(EditorFontType.PLAIN)
        val editorFontSize = editorFont.size

        font = font.deriveFont(editorFontSize.toFloat() * 0.85f)
        foreground = Color(255, 255, 255) // 字体颜色设为白色

        addMouseListener(object : MouseAdapter() {
            override fun mouseEntered(e: MouseEvent) {
                isHovered = true
                repaint()
            }

            override fun mouseExited(e: MouseEvent) {
                isHovered = false
                repaint()
            }
        })
    }

    override fun paintComponent(g: Graphics) {
        val g2 = g.create() as Graphics2D
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

        val width = width.toFloat()
        val height = height.toFloat()
        val arc = 6f

        // 按钮背景：高亮时紫色，非高亮时与弹框背景同色
        g2.color = if (isHovered) hoverColor else normalColor
        g2.fill(RoundRectangle2D.Float(0f, 0f, width, height, arc, arc))

        super.paintComponent(g)
        g2.dispose()
    }
}

class AIMIToolTipComponent(editor: Editor, x: Int, y: Int) :
    JBPanel<AIMIToolTipComponent>() {
    private var aimiIconLabel: JLabel
    private var addToChatButton: AIMIStyledButton
    private var optimizeCodeButton: AIMIStyledButton
    // private var generateCommentButton: AIMIStyledButton
    // private var explainCodeButton: AIMIStyledButton

    init {
        layout = FlowLayout(FlowLayout.LEFT, 6, 6) // 调整按钮间距

        // Make the background transparent
        isOpaque = false
        background = Color(0, 0, 0, 0)

        val addToChatShortcut = ShortcutUtils.getActionInfo("aimi.focusInputWithoutClear").second
        val optimizeCodeShortcut = ShortcutUtils.getActionInfo("aimi.focusInput").second

        val buttonHeight = 24 // 稍微增加按钮高度
        val buttonHorizontalPadding = 0
        val buttonVerticalPadding = 6

        // 创建 AIMI 图标标签
        aimiIconLabel = JLabel(AIMIIcons.AIMI)
        aimiIconLabel.preferredSize = Dimension(buttonHeight, buttonHeight) // 设置图标大小

        // 创建按钮
        addToChatButton = AIMIStyledButton("添加到对话 (${addToChatShortcut})")
        optimizeCodeButton = AIMIStyledButton("优化代码 (${optimizeCodeShortcut})")
        // generateCommentButton = AIMIStyledButton("生成注释 (${cmdCtrlChar})")
        // explainCodeButton = AIMIStyledButton("解读代码 (${cmdCtrlChar})")

        // 设置按钮大小
        val buttons = listOf(addToChatButton, optimizeCodeButton /*generateCommentButton, explainCodeButton*/)
        buttons.forEach { button ->
            // val preferredWidth = button.preferredSize.width + (2 * buttonHorizontalPadding)
            button.preferredSize = Dimension(button.preferredSize.width, buttonHeight)
        }

        // 添加事件监听器
        addToChatButton.addActionListener { e: ActionEvent? ->
            focusContinueInput(editor.project, newSession = false, shouldRun = false)
            editor.contentComponent.remove(this)
        }

        optimizeCodeButton.addActionListener { e: ActionEvent? ->
            focusContinueInput(editor.project, newSession = true, shouldRun = true)
            editor.contentComponent.remove(this)
        }

        // 首先添加图标，然后添加按钮
        add(aimiIconLabel)
        buttons.forEach(::add)

        // 计算总宽度和高度，包含图标
        val iconWidth = aimiIconLabel.preferredSize.width
        val totalWidth = iconWidth + buttons.sumOf { it.preferredSize.width } + (buttons.size + 2) * 6 // 调整边距，+2是因为多了图标
        val totalHeight = buttonHeight + (2 * buttonVerticalPadding)

        // 设置组件位置
        val yPosition = y - (totalHeight / 2)
        setBounds(x, yPosition, totalWidth, totalHeight)
    }

    override fun paintComponent(g: Graphics) {
        val g2 = g.create() as Graphics2D
        g2.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON)

        // 绘制弹框背景 - 深灰色背景
        g2.color = bgColor
        g2.fill(RoundRectangle2D.Float(0f, 0f, width.toFloat(), height.toFloat(), 8f, 8f))

        super.paintComponent(g)
        g2.dispose()
    }

    companion object {
        val bgColor = Color(60, 63, 65, 255) // 深灰色背景，不透明
    }
}