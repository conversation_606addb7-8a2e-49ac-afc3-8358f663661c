package com.taobao.mc.aimi.execution

import com.intellij.execution.configurations.ConfigurationFactory
import com.intellij.execution.configurations.ConfigurationType
import com.intellij.icons.AllIcons
import com.intellij.openapi.components.BaseState
import com.intellij.openapi.project.Project
import javax.swing.Icon

const val AIMI_CONFIGURATION_NAME = "AIMI"

class AIMIConfigurationType : ConfigurationType {
    override fun getDisplayName(): String = AIMI_CONFIGURATION_NAME
    override fun getConfigurationTypeDescription(): String = "Run shell script with AIMI terminal integration"
    override fun getIcon(): Icon = AllIcons.Actions.Execute
    override fun getId(): String = "AIMIShellScriptRunConfiguration"
    override fun isManaged(): Boolean = false

    override fun getConfigurationFactories(): Array<ConfigurationFactory> = arrayOf(AIMIConfigurationFactory(this))
}

class AIMIConfigurationFactory(type: ConfigurationType) : ConfigurationFactory(type) {
    override fun getId(): String = "AIMIShellScriptFactory"
    
    override fun createTemplateConfiguration(project: Project): AIMIRunConfiguration {
        return AIMIRunConfiguration(project, this, AIMI_CONFIGURATION_NAME)
    }

    override fun getOptionsClass(): Class<out BaseState?>? {
        return AIMIRunConfigurationOptions::class.java
    }

    override fun isApplicable(project: Project): Boolean {
        return false
    }
}