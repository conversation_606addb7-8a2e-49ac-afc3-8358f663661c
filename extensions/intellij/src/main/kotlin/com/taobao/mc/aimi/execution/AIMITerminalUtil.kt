package com.taobao.mc.aimi.execution

import com.taobao.mc.aimi.logger.LoggerManager
import com.intellij.execution.ui.RunContentManager
import com.intellij.openapi.project.Project
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * AIMI终端工具类，用于获取终端执行结果
 */
object AIMITerminalUtil {
    private val logger = LoggerManager.getLogger(AIMITerminalUtil::class.java)


    /**
     * 获取指定终端ID的当前执行结果（非阻塞）
     * @param project 项目
     * @param terminalId 终端ID
     * @return 当前执行结果，如果找不到对应的终端则返回null
     */
    fun getCurrentTerminalOutput(project: Project, terminalId: Long): String? {
        // 查找所有运行中的进程
        val descriptors = RunContentManager.getInstance(project).allDescriptors

        // 遍历所有描述符，查找匹配的终端ID
        for (descriptor in descriptors) {
            val processHandler = descriptor.processHandler ?: continue
            val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

            // 如果找到匹配的终端ID，返回其当前输出
            if (terminalInfo.terminalId == terminalId) {
                return terminalInfo.getCurrentOutput()
            }
        }

        return null
    }

    /**
     * 获取指定终端ID的执行结果（等待进程终止）
     * @param project 项目
     * @param terminalId 终端ID
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 执行结果，如果找不到对应的终端则返回null
     */
    suspend fun getTerminalOutput(project: Project, terminalId: Long, timeoutMillis: Long = 30000): String? {
        return withContext(Dispatchers.IO) {
            // 查找所有运行中的进程
            val descriptors = RunContentManager.getInstance(project).allDescriptors

            // 遍历所有描述符，查找匹配的终端ID
            for (descriptor in descriptors) {
                val processHandler = descriptor.processHandler ?: continue
                val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

                // 如果找到匹配的终端ID，等待并返回其输出
                if (terminalInfo.terminalId == terminalId) {
                    return@withContext terminalInfo.getOutput(timeoutMillis)
                }
            }

            null
        }
    }

    /**
     * 获取指定终端ID的执行结果和状态（等待进程终止）
     * @param project 项目
     * @param terminalId 终端ID
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 执行结果和状态，如果找不到对应的终端则返回null
     */
    suspend fun getTerminalOutputWithStatus(project: Project, terminalId: Long, timeoutMillis: Long = 30000): TerminalResult {
        return withContext(Dispatchers.IO) {
            // 查找所有运行中的进程
            val descriptors = RunContentManager.getInstance(project).allDescriptors

            // 遍历所有描述符，查找匹配的终端ID
            for (descriptor in descriptors) {
                val processHandler = descriptor.processHandler ?: continue
                val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

                // 如果找到匹配的终端ID，等待并返回其输出和状态
                if (terminalInfo.terminalId == terminalId) {
                    return@withContext terminalInfo.getOutputWithStatus(timeoutMillis)
                }
            }

            TerminalResult("", false, 1)
        }
    }

    /**
     * 获取最近一个活动终端的当前执行结果（非阻塞）
     * @param project 项目
     * @return 当前执行结果，如果没有活动终端则返回null
     */
    fun getCurrentActiveTerminalOutput(project: Project): String? {
        val terminalInfo = AIMITerminalInfo.findActiveTerminal(project) ?: return null
        return terminalInfo.getCurrentOutput()
    }

    /**
     * 获取最近一个活动终端的执行结果（等待进程终止）
     * @param project 项目
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 执行结果，如果没有活动终端则返回null
     */
    suspend fun getActiveTerminalOutput(project: Project, timeoutMillis: Long = 30000): String? {
        return withContext(Dispatchers.IO) {
            val terminalInfo = AIMITerminalInfo.findActiveTerminal(project) ?: return@withContext null
            terminalInfo.getOutput(timeoutMillis)
        }
    }

    /**
     * 获取最近一个活动终端的执行结果和状态（等待进程终止）
     * @param project 项目
     * @param timeoutMillis 等待超时时间（毫秒），默认为30秒
     * @return 执行结果和状态，如果没有活动终端则返回null
     */
    suspend fun getActiveTerminalOutputWithStatus(project: Project, timeoutMillis: Long = 30000): TerminalResult? {
        return withContext(Dispatchers.IO) {
            val terminalInfo = AIMITerminalInfo.findActiveTerminal(project) ?: return@withContext null
            terminalInfo.getOutputWithStatus(timeoutMillis)
        }
    }

    /**
     * 获取所有终端的当前执行结果（非阻塞）
     * @param project 项目
     * @return 终端ID到当前执行结果的映射
     */
    fun getAllCurrentTerminalOutputs(project: Project): Map<Long, String> {
        val result = mutableMapOf<Long, String>()
        val descriptors = RunContentManager.getInstance(project).allDescriptors

        for (descriptor in descriptors) {
            val processHandler = descriptor.processHandler ?: continue
            val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

            result[terminalInfo.terminalId] = terminalInfo.getCurrentOutput()
        }

        return result
    }

    /**
     * 获取所有终端的执行结果和状态（非阻塞）
     * @param project 项目
     * @return 终端ID到执行结果和状态的映射
     */
    fun getAllCurrentTerminalOutputsWithStatus(project: Project): Map<Long, TerminalResult> {
        val result = mutableMapOf<Long, TerminalResult>()
        val descriptors = RunContentManager.getInstance(project).allDescriptors

        for (descriptor in descriptors) {
            val processHandler = descriptor.processHandler ?: continue
            val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

            val output = terminalInfo.getCurrentOutput()
            val isTerminated = terminalInfo.isTerminated()
            val exitCode = terminalInfo.getExitCode()

            result[terminalInfo.terminalId] = TerminalResult(output, isTerminated, exitCode)
        }

        return result
    }

    /**
     * 清空指定终端的输出缓冲区
     * @param project 项目
     * @param terminalId 终端ID
     * @return 是否成功清空
     */
    fun clearTerminalOutput(project: Project, terminalId: Long): Boolean {
        val descriptors = RunContentManager.getInstance(project).allDescriptors

        for (descriptor in descriptors) {
            val processHandler = descriptor.processHandler ?: continue
            val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY) ?: continue

            if (terminalInfo.terminalId == terminalId) {
                terminalInfo.clearOutput()
                return true
            }
        }

        return false
    }

    /**
     * 获取所有运行中的终端信息
     * @param project 项目
     * @return 终端ID列表
     */
    fun getRunningTerminals(project: Project): List<Long> {
        val result = mutableListOf<Long>()
        val descriptors = RunContentManager.getInstance(project).allDescriptors

        logger.info("Total descriptors: ${descriptors.size}")

        for (descriptor in descriptors) {
            val processHandler = descriptor.processHandler
            if (processHandler == null) {
                logger.info("Process handler is null for descriptor: ${descriptor.displayName}")
                continue
            }

            val terminalInfo = processHandler.getUserData(AIMITerminalInfo.TERMINAL_INFO_KEY)
            if (terminalInfo == null) {
                logger.info("Terminal info is null for process: ${descriptor.displayName}")
                continue
            }

            logger.info("Found terminal info: terminalId=${terminalInfo.terminalId}, command=${terminalInfo.command}")

            // 只添加仍在运行的终端
            if (!processHandler.isProcessTerminated) {
                result.add(terminalInfo.terminalId)
            } else {
                logger.info("Process is terminated for terminal: ${terminalInfo.terminalId}")
            }
        }

        logger.info("Running terminals: $result")
        return result
    }
}
