package com.taobao.mc.aimi.ext.protocol

import com.taobao.mc.aimi.ext.Range

data class CopyTextParams(
    val text: String
)

data class ApplyToFileParams(
    val text: String,
    val streamId: String,
    val filepath: String? = null,
    val toolCallId: String? = null,
    val range: Range? = null,
    val isSearchReplace: Boolean? = null,
    var messageId: String? = null,
)

data class InsertAtCursorParams(
    val text: String
)
