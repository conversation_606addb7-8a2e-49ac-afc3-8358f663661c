package com.taobao.mc.aimi.ext.editor

import com.intellij.openapi.components.Service
import com.intellij.openapi.editor.Editor
import com.taobao.mc.aimi.logger.LoggerManager

@Service(Service.Level.PROJECT)
class DiffStreamService {
    private val logger = LoggerManager.getLogger(DiffStreamService::class.java)

    private val handlers = mutableMapOf<Editor, DiffStreamHandler>()

    fun register(handler: DiffStreamHandler, editor: Editor) {
        if (handlers.contains<PERSON>ey(editor)) {
            handlers[editor]?.rejectAll()
        }
        handlers[editor] = handler
        logger.info("Registered handler for editor")
    }

    fun reject(editor: Editor) {
        handlers[editor]?.rejectAll()
        handlers.remove(editor)
    }

    fun accept(editor: Editor) {
        handlers[editor]?.acceptAll()
        handlers.remove(editor)
    }


    /**
     * 检查指定编辑器的处理程序是否正在运行。
     *
     * @param editor 要检查的编辑器实例。
     * @return 如果处理程序正在运行，则返回true；否则，返回false。
     */
    fun isRunning(editor: Editor): Boolean {
        return handlers[editor]?.isRunning ?: false
    }
}