import { getLastNUriRelativePathParts } from "../../util/uri";
import {
  AutocompleteClipboardSnippet,
  AutocompleteCodeSnippet,
  AutocompleteDiffSnippet,
  AutocompleteRepoSnippet,
  AutocompleteSnippet,
  AutocompleteSnippetType,
  AutocompleteStaticSnippet,
} from "../snippets/types";
import { HelperVars } from "../util/HelperVars";
import { countTokens } from "../../llm/countTokens";

const getCommentMark = (helper: HelperVars) => {
  return helper.lang.singleLineComment;
};

export const addCommentMarks = (text: string, helper: HelperVars) => {
  const commentMark = getCommentMark(helper);
  return text
    .trim()
    .split("\n")
    .map((line) => `${commentMark} ${line}`)
    .join("\n");
};

const formatClipboardSnippet = (
  snippet: AutocompleteClipboardSnippet,
  workspaceDirs: string[],
  multiFileSupport: boolean = false,
): AutocompleteCodeSnippet => {
  return formatCodeSnippet(
    {
      filepath: "file:///Untitled.txt",
      content: snippet.content,
      type: AutocompleteSnippetType.Code,
    },
    workspaceDirs,
    multiFileSupport,
  );
};

const formatCodeSnippet = (
  snippet: AutocompleteCodeSnippet,
  workspaceDirs: string[],
  multiFileSupport: boolean = false,
): AutocompleteCodeSnippet => {
  const content = multiFileSupport
    ? `<|file_sep|>${snippet.filepath}\n${snippet.content}`
    : `Path: ${snippet.filepath}\n${snippet.content}`;
  return {
    ...snippet,
    content,
  };
};

const formatRepoSnippet = (
  snippet: AutocompleteRepoSnippet,
  workspaceDirs: string[],
): AutocompleteRepoSnippet => {
  return {
    ...snippet,
    content: `<|repo_name|>${snippet.content}`,
  };
};

const formatDiffSnippet = (
  snippet: AutocompleteDiffSnippet,
): AutocompleteDiffSnippet => {
  return snippet;
};

const formatStaticSnippet = (
  snippet: AutocompleteStaticSnippet,
): AutocompleteStaticSnippet => {
  return snippet;
};

const commentifySnippet = (
  helper: HelperVars,
  snippet: AutocompleteSnippet,
): AutocompleteSnippet => {
  return {
    ...snippet,
    content: addCommentMarks(snippet.content, helper),
  };
};

export const formatSnippets = (
  helper: HelperVars,
  snippets: AutocompleteSnippet[],
  workspaceDirs: string[],
  multiFileSupport: boolean = false,
): string => {
  const currentFilepathComment = addCommentMarks(
    getLastNUriRelativePathParts(workspaceDirs, helper.filepath, 2),
    helper,
  );

  const tokenCount = countTokens(
    helper.prunedCaretWindow + (multiFileSupport ? "" : currentFilepathComment),
    helper.modelName,
  );
  let remainingTokenCount = helper.options.maxPromptTokens - tokenCount;

  const items = snippets
    .map((snippet) => {
      switch (snippet.type) {
        case AutocompleteSnippetType.RepoName:
          return formatRepoSnippet(snippet, workspaceDirs);
        case AutocompleteSnippetType.Code:
          return formatCodeSnippet(snippet, workspaceDirs, multiFileSupport);
        case AutocompleteSnippetType.Diff:
          return formatDiffSnippet(snippet);
        case AutocompleteSnippetType.Clipboard:
          return formatClipboardSnippet(
            snippet,
            workspaceDirs,
            multiFileSupport,
          );
        case AutocompleteSnippetType.Static:
          return formatStaticSnippet(snippet);
      }
    })
    .map((item) => {
      return commentifySnippet(helper, item).content;
    });
  // 根据remainingTokenCount截断
  const remainingItems = items
    .filter((item) => {
      const tokens = countTokens(item, helper.modelName);
      const valid = tokens <= remainingTokenCount;
      if (valid) {
        remainingTokenCount -= tokens + 1;
      }
      return valid;
    })
    .join("\n");
  if (multiFileSupport) {
    return remainingItems;
  }
  return `${remainingItems}\n${currentFilepathComment}`;
};
