export interface UserVO {
  /** 工号 */
  empId?: string;
  /** 花名 */
  nickName?: string;
  /** 真名 */
  name?: string;
  /** 英文名 */
  englishName?: string;
  /** 邮箱 */
  email?: string;
  /** 职位描述：eg.技术-基础平台-开发 */
  jobName?: string;
  /**
   * 员工类型
   * 正式\外包
   */
  empType?: string;
  /** 离职状态 A：在职 I：离职 */
  workStatus?: string;
  /** 主管工号 */
  superWorkNo?: string;
  /** 公司编号 */
  corpDeptNo?: string;
  /** 公司名称，淘天集团 */
  corpName?: string;
  /** 公司英文名 */
  corpEnName?: string;
  /** BG编号 */
  bgDeptNo?: string;
  /** BG名称，淘天集团-业务技术 */
  bgName?: string;
  /** BG英文名 */
  bgEnName?: string;
  /** BU编号 */
  buDeptNo?: string;
  /** BU名称，淘天集团-业务技术-终端平台 */
  buName?: string;
  /** BU名称，淘天集团-业务技术-终端平台 */
  bu?: string;
  /** BU英文名 */
  buEnName?: string;
  /** 部门编号 */
  deptNo?: string;
  /** 部门名称 */
  deptName?: string;
  /** 部门英文名称 */
  deptEnName?: string;
  /** 钉钉Id */
  dingTalkId?: string;
  /** 钉钉Nick */
  dingTalkNick?: string;
  /** 钉钉地址 */
  dingTalkUrl?: string;
  /** 头像 */
  avatar?: string;
  /** 内外地址 */
  workUrl?: string;
}
